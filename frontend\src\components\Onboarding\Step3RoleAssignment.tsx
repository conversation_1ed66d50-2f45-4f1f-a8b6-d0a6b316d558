import { Check, Loader, UserPlus, XCircle, Users } from "lucide-react";
import React, { useEffect, useState } from "react";
import Select from "react-select";
import { useOnboarding } from "../../contexts/OnboardingContext";

interface User {
  id: string;
  userName: string;
  email: string;
  firstName: string;
  lastName: string;
  emailConfirmed: boolean;
  phoneNumber: string;
  phoneNumberConfirmed: boolean;
  imageUrl: string | null;
  isActive: boolean;
  roles: string[];
}

interface Role {
  id: string;
  name: string;
  description: string;
}

interface ApplicationDetails {
  id: string;
  value: string;
  isActive: boolean;
  expiresAt: string;
  createdAt: string;
  isDeleted: boolean;
  tenantId: string;
  productId: string;
  product: {
    id: string;
    name: string;
    description?: string;
    applicationUrl?: string;
    icon?: string;
  };
  tenant: {
    id: string;
    name: string;
    adminEmail: string;
    isActive: boolean;
    validUpto: string;
    issuer: string | null;
    identifier: string;
    displayPrefix: string;
  };
}

interface Step3RoleAssignmentProps {
  tenantId: string;
  token: string;
  users: User[];
  applicationDetails: ApplicationDetails;
  tenantData?: {
    id: string;
    name: string;
    adminEmail: string;
    isActive: boolean;
    validUpto: string;
    issuer: string | null;
    product?: {
      id: string;
      name: string;
    };
  };
  onComplete: (data: any) => void;
  onError?: (error: string) => void;
}

const Step3RoleAssignment: React.FC<Step3RoleAssignmentProps> = (props) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [success, setSuccess] = useState<string>("");
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [roleUserSelections, setRoleUserSelections] = useState<{
    [roleId: string]: string[];
  }>({});

  const { tenantId, onComplete } = props;
  const { fetchUsersAndRoles, fetchRoles, assignRoles } = useOnboarding();

  // Check if any role has users assigned
  const hasAnyAssignments = () => {
    return Object.values(roleUserSelections).some((users) => users.length > 0);
  };

  // Handle bulk role assignment
  const handleAssignRoles = async () => {
    // Prevent multiple submissions
    if (submitting) return;

    if (!hasAnyAssignments()) {
      setError("Please assign at least one user to any role");
      return;
    }

    setSubmitting(true);
    setError("");
    setSuccess("");

    try {
      // Prepare the role assignments from the new structure
      const roleAssignments = Object.entries(roleUserSelections)
        .filter(([_, userIds]) => userIds.length > 0)
        .flatMap(([roleId, userIds]) =>
          userIds.map((userId) => ({
            userId,
            roleId,
          }))
        );

      // Use the context to assign roles - use product ID from applicationDetails instead of tenantData
      const productId = props.applicationDetails?.productId || props.tenantData?.product?.id;
      await assignRoles(
        tenantId,
        roleAssignments,
        productId
      );

      // Reset selections
      setRoleUserSelections({});

      // Show success message
      setSuccess("Roles assigned successfully!");
      setTimeout(() => setSuccess(""), 3000);

      // Move to next step after a short delay
      setTimeout(() => onComplete({ success: true }), 3000);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to assign roles. Please try again.";
      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Use users passed from Step2 to avoid duplicate getUsers API call
        if (props.users && props.users.length > 0) {
          console.log('Using users from Step2:', props.users);
          setUsers(props.users);

          // Only fetch roles since we already have users
          const rolesData = await fetchRoles(tenantId, true);
          console.log('Fetched roles:', rolesData);
          setRoles(rolesData);
        } else {
          // Fallback: fetch both users and roles if no users passed from Step2
          const { users: usersData, roles: rolesData } = await fetchUsersAndRoles(
            tenantId,
            true
          );
          console.log('Fetched users:', usersData);
          console.log('Fetched roles:', rolesData);
          setRoles(rolesData);
          setUsers(usersData);
        }
      } catch (err) {
        setError("Failed to load data. Please refresh the page.");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [tenantId, props.users, fetchUsersAndRoles]);

  return (
    <div className="max-w-6xl mx-auto space-y-4">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-xl font-bold text-gray-900 mb-1 ml-2">
          Assign User Roles
        </h2>
        <p className="text-xs text-gray-600 max-w-xl mx-auto">
          Efficiently assign multiple users to roles and manage permissions
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 rounded-lg p-2">
          <div className="flex items-center">
            <XCircle className="h-3 w-3 text-red-400" />
            <div className="ml-2 text-xs text-red-700">{error}</div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 rounded-lg p-2">
          <div className="flex items-center">
            <Check className="h-3 w-3 text-green-400" />
            <div className="ml-2 text-xs text-green-700">{success}</div>
          </div>
        </div>
      )}

      {/* Main Content - Two Column Layout */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Left Column - Roles */}
        <div className="xl:col-span-2">
          <div className="bg-white rounded-lg shadow border border-gray-100 overflow-visible">
            <div className="bg-slate-600 rounded-t-lg px-3 py-2">
              <div className="flex items-center space-x-2">
                <div className="flex-shrink-0">
                  <div className="w-6 h-6 bg-slate-500 rounded-lg flex items-center justify-center">
                    <Users className="w-3 h-3 text-slate-100" />
                  </div>
                </div>
                <div>
                  <h3 className="text-base font-semibold text-white">
                    Available Roles
                  </h3>
                  <p className="text-slate-200 text-xs">
                    Select roles and assign users to them
                  </p>
                </div>
              </div>
            </div>

            <div className="p-4 pb-8 overflow-visible">
              {loading ? (
                <div className="text-center py-6">
                  <div className="inline-flex items-center justify-center w-10 h-10 bg-primary-50 rounded-full mb-2">
                    <Loader className="animate-spin h-5 w-5 text-primary-500" />
                  </div>
                  <p className="text-gray-600 font-medium text-xs">
                    Loading roles...
                  </p>
                </div>
              ) : roles.length > 0 ? (
                <div className="space-y-3">
                  {roles.map((role, index) => (
                    <div key={role.id} className="group relative">
                      <div className="bg-white rounded-lg p-3 border border-slate-200 hover:border-slate-300 hover:shadow-md transition-all duration-200">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <div className="flex-shrink-0">
                                <div className="w-5 h-5 bg-slate-600 rounded-lg flex items-center justify-center">
                                  <span className="text-white font-semibold text-xs">
                                    {index + 1}
                                  </span>
                                </div>
                              </div>
                              <h4 className="text-sm font-semibold text-slate-800">
                                {role.name}
                              </h4>
                            </div>
                            {role.description && (
                              <p className="text-slate-600 text-xs leading-relaxed ml-7">
                                {role.description}
                              </p>
                            )}
                          </div>
                          <div className="flex items-center space-x-1 bg-slate-50 rounded-lg px-2 py-1 border border-slate-200">
                            <Users className="h-3 w-3 text-slate-600" />
                            <span className="text-xs font-medium text-slate-700">
                              {roleUserSelections[role.id]?.length || 0}
                            </span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Assign Users to {role.name}
                            </label>
                            <div className="relative">
                              <Select
                                isMulti
                                instanceId={`role-select-${role.id}`}
                                key={`role-select-${role.id}`}
                                value={(() => {
                                  const selectedUserIds = roleUserSelections[role.id] || [];
                                  const selectedOptions = selectedUserIds
                                    .map((userId) => {
                                      const user = users.find(u => u.id === userId);
                                      return user ? {
                                        value: user.id,
                                        label: `${user.firstName} ${user.lastName}`,
                                        email: user.email,
                                      } : null;
                                    })
                                    .filter((option): option is { value: string; label: string; email: string } => option !== null);
                                  return selectedOptions;
                                })()}
                                onChange={(selectedOptions) => {
                                  const selectedUserIds = selectedOptions
                                    ? selectedOptions.map(
                                      (option) => option.value
                                    )
                                    : [];
                                  setRoleUserSelections((prev) => ({
                                    ...prev,
                                    [role.id]: selectedUserIds,
                                  }));
                                }}
                                options={users.map((user) => ({
                                  value: user.id,
                                  label: `${user.firstName} ${user.lastName}`,
                                  email: user.email,
                                }))}
                                placeholder="🔍 Search and select users..."
                                className="text-xs"
                                classNamePrefix="react-select"
                                isDisabled={loading || submitting}
                                noOptionsMessage={() => "No users available"}
                                styles={{
                                  control: (base, state) => ({
                                    ...base,
                                    borderColor: state.isFocused
                                      ? "#6B7280"
                                      : "#D1D5DB",
                                    boxShadow: state.isFocused
                                      ? "0 0 0 2px rgba(107, 114, 128, 0.1)"
                                      : "none",
                                    borderRadius: "0.375rem",
                                    padding: "0.125rem",
                                    minHeight: "2rem",
                                    fontSize: "0.75rem",
                                  }),
                                  menu: (base) => ({
                                    ...base,
                                    zIndex: 9999,
                                    position: "absolute",
                                    fontSize: "0.75rem",
                                  }),
                                  menuPortal: (base) => ({
                                    ...base,
                                    zIndex: 9999,
                                  }),
                                  multiValue: (base) => ({
                                    ...base,
                                    backgroundColor: "#F3F4F6",
                                    borderRadius: "0.25rem",
                                    fontSize: "0.625rem",
                                  }),
                                  multiValueLabel: (base) => ({
                                    ...base,
                                    color: "#374151",
                                    fontWeight: "500",
                                    fontSize: "0.625rem",
                                  }),
                                  multiValueRemove: (base) => ({
                                    ...base,
                                    color: "#6B7280",
                                    ":hover": {
                                      backgroundColor: "#E5E7EB",
                                      color: "#374151",
                                    },
                                  }),
                                }}
                                menuPortalTarget={document.body}
                                menuPosition="fixed"
                              />
                            </div>
                          </div>

                          {roleUserSelections[role.id]?.length > 0 && (
                            <div className="bg-white rounded-lg p-2 border border-gray-200">
                              <h5 className="text-xs font-medium text-gray-700 mb-1">
                                Selected ({roleUserSelections[role.id].length})
                              </h5>
                              <div className="flex flex-wrap gap-1">
                                {roleUserSelections[role.id].map((userId, index) => {
                                  const user = users.find(
                                    (u) => u.id === userId
                                  );
                                  return user ? (
                                    <span
                                      key={`${role.id}-${userId}-${index}`}
                                      className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700 border border-gray-200"
                                    >
                                      {user.firstName} {user.lastName}
                                    </span>
                                  ) : null;
                                })}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <div className="inline-flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full mb-2">
                    <Users className="w-5 h-5 text-gray-400" />
                  </div>
                  <p className="text-gray-600 font-medium text-xs">
                    No roles available
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Column - Summary */}
        <div className="xl:col-span-1">
          <div className="bg-white rounded-lg shadow border border-gray-100 overflow-hidden sticky top-4">
            <div className="bg-slate-600 px-3 py-2">
              <div className="flex items-center space-x-2">
                <div className="flex-shrink-0">
                  <div className="w-6 h-6 bg-slate-500 rounded-lg flex items-center justify-center">
                    <Check className="w-3 h-3 text-slate-100" />
                  </div>
                </div>
                <div>
                  <h3 className="text-base font-semibold text-white">
                    Summary
                  </h3>
                  <p className="text-slate-200 text-xs">Review assignments</p>
                </div>
              </div>
            </div>

            <div className="p-3">
              {hasAnyAssignments() ? (
                <div className="space-y-3">
                  <div className="bg-slate-100 rounded-lg p-2 border border-slate-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1">
                        <div className="w-5 h-5 bg-slate-600 rounded-full flex items-center justify-center">
                          <Users className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-xs font-medium text-slate-700">
                          Total
                        </span>
                      </div>
                      <span className="text-base font-bold text-slate-800">
                        {Object.values(roleUserSelections).reduce(
                          (total, users) => total + users.length,
                          0
                        )}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {Object.entries(roleUserSelections)
                      .filter(([_, userIds]) => userIds.length > 0)
                      .map(([roleId, userIds]) => {
                        const role = roles.find((r) => r.id === roleId);
                        return role ? (
                          <div
                            key={roleId}
                            className="bg-white rounded-lg p-2 border border-slate-200"
                          >
                            <div className="flex items-center justify-between mb-1">
                              <div className="flex items-center space-x-1">
                                <div className="w-4 h-4 bg-slate-600 rounded-full flex items-center justify-center">
                                  <span className="text-white font-semibold text-xs">
                                    {userIds.length}
                                  </span>
                                </div>
                                <h4 className="text-xs font-semibold text-slate-800 truncate">
                                  {role.name}
                                </h4>
                              </div>
                            </div>
                            <div className="space-y-0.5">
                              {userIds.slice(0, 3).map((userId, index) => {
                                const user = users.find((u) => u.id === userId);
                                return user ? (
                                  <div
                                    key={`${roleId}-summary-${userId}-${index}`}
                                    className="text-xs text-gray-600 truncate"
                                  >
                                    • {user.firstName} {user.lastName}
                                  </div>
                                ) : null;
                              })}
                              {userIds.length > 3 && (
                                <div className="text-xs text-gray-500">
                                  +{userIds.length - 3} more
                                </div>
                              )}
                            </div>
                          </div>
                        ) : null;
                      })}
                  </div>

                  {/* Submit Button */}
                  <div className="pt-3 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={handleAssignRoles}
                      disabled={submitting || loading || !hasAnyAssignments()}
                      className={`w-full flex justify-center items-center py-2 px-3 border border-transparent rounded-lg shadow text-xs text-white transition-all duration-200 ${submitting || loading || !hasAnyAssignments()
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500"
                        }`}
                    >
                      {submitting ? (
                        <>
                          <Loader className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" />
                          <span>Assigning...</span>
                        </>
                      ) : (
                        <>
                          <UserPlus className="-ml-1 mr-1 h-3 w-3" />
                          <span>
                            Assign (
                            {Object.values(roleUserSelections).reduce(
                              (total, users) => total + users.length,
                              0
                            )}
                            )
                          </span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-6">
                  <div className="inline-flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full mb-3">
                    <Users className="w-5 h-5 text-gray-400" />
                  </div>
                  <h4 className="text-xs font-semibold text-gray-700 mb-1">
                    No Assignments
                  </h4>
                  <p className="text-gray-500 text-xs max-w-xs mx-auto">
                    Select users for roles to get started.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step3RoleAssignment;
