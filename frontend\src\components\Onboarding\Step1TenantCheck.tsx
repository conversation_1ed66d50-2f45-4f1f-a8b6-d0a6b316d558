import React, { useState, useEffect } from "react";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { apiService } from "../../services/apiService";

interface ProductStatus {
  id?: string;
  name?: string;
  isUserImported: boolean;
  isRoleAssigned: boolean;
  isOnboardCompleted?: boolean;
}

interface TenantData {
  id: string;
  name: string;
  adminEmail: string;
  isActive: boolean;
  validUpto: string;
  issuer: string | null;
  product?: ProductStatus;
}

interface Step1Props {
  tenantId: string;
  existingTenantData?: TenantData | null; // Add existing tenant data prop
  applicationDetails: {
    id: string;
    value: string;
    isActive: boolean;
    expiresAt: string;
    createdAt: string;
    isDeleted: boolean;
    tenantId: string;
    productId: string;
    product: {
      id: string;
      name: string;
      description?: string;
      applicationUrl?: string;
      icon?: string;
    };
    tenant: {
      id: string;
      name: string;
      adminEmail: string;
      isActive: boolean;
      validUpto: string;
      issuer: string | null;
      identifier: string;
      displayPrefix: string;
    };
  };
  onComplete: (data: TenantData) => void;
  onError?: (error: string) => void;
}

const Step1TenantCheck: React.FC<Step1Props> = ({
  tenantId,
  existingTenantData,
  applicationDetails,
  onComplete,
  onError,
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tenantData, setTenantData] = useState<TenantData | null>(existingTenantData || null);
  const [hasProcessed, setHasProcessed] = useState(false);

  const createNewTenant = async (): Promise<TenantData> => {
    try {
      if (!applicationDetails?.tenant || !applicationDetails?.product) {
        throw new Error(
          "Tenant or product details not found in application details"
        );
      }

      const { id, name, adminEmail, isActive, validUpto, issuer } =
        applicationDetails.tenant;
      const product = applicationDetails.product;

      // Create the tenant using API service
      const tenantPayload = {
        id,
        name,
        adminEmail: adminEmail || "",
        isActive: isActive ?? true,
        validUpto:
          validUpto ||
          new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        issuer: issuer || null,
      };

      await apiService.createTenant(tenantPayload);

      // Prepare product registration payload
      const productsPayload = {
        blueprintProductId: "00000000-0000-0000-0000-000000000000",
        productName: product.name || "",
        productPrefix: product.name || "",
        subscribedVersion: "1.0",
        currentVersion: "1.0",
        expiryDate: applicationDetails.expiresAt,
        blueprintServerHost: "",
        blueprintDatabaseName: "",
        isSubscriptionActive: true,
        blueprintName: "",
        blueprintPrefix: "",
        blueprintVersion: "",
        name: product.name || "",
        prefix: product.name || "",
        version: "1.0",
        showSequence: 0,
        value: product.name || "",
        isActive: true,
        isUserImported: false,
        isRoleAssigned: false,
        apiKey: applicationDetails.value || "",
        isOnboardCompleted: false,
        applicationUrl: product.applicationUrl || "",
        icon: product.icon || "",
      };

      // Register product details using API service
      await apiService.createProductWithSubscription(name, productsPayload);

      // Create a minimal tenant data object for the new tenant
      const tenantData: TenantData = {
        id,
        name,
        adminEmail: adminEmail || "",
        isActive: isActive ?? true,
        validUpto: validUpto || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        issuer: issuer || null,
        product: {
          id: applicationDetails.productId || "",
          name: product.name || "",
          isUserImported: false,
          isRoleAssigned: false,
          isOnboardCompleted: false,
        }
      };

      // Get product status flags from the new API endpoint
      if (applicationDetails.productId) {
        try {
          const productData = await apiService.getProductById(
            applicationDetails.productId,
            id,
            true // Use cache to prevent duplicate calls
          );

          // Update tenant data with product status flags from the new API
          if (productData) {
            // Ensure the product object exists before updating
            if (tenantData.product) {
              tenantData.product.isUserImported = productData.isUserImported || false;
              tenantData.product.isRoleAssigned = productData.isRoleAssigned || false;
              tenantData.product.isOnboardCompleted = productData.isOnboardCompleted || false;
            }
          }
        } catch (productError) {
          console.warn('Failed to fetch product status flags:', productError);

          // Check if this is a "Product not found" error
          const errorMessage = productError instanceof Error ? productError.message : '';
          if (errorMessage.includes('Product not found')) {
            console.error('Product not found - removing product object to stop onboarding at step 1');
            // Remove the product object to ensure onboarding stops at step 1
            tenantData.product = undefined;
            throw new Error('Product not found. Please contact your administrator to configure the product for this tenant.');
          }
          // For other errors, continue with existing product object (if any)
        }
      }

      return tenantData;
    } catch (error) {
      // Re-throw the original error from API service (which has better error handling)
      throw error;
    }
  };

  const processNewTenant = async (): Promise<void> => {
    if (!tenantId) {
      const errorMsg = "Tenant ID is required";
      setError(errorMsg);
      onError?.(errorMsg);
      setLoading(false);
      return;
    }

    if (hasProcessed) return; // Prevent multiple calls

    try {
      setLoading(true);
      setError(null);
      setHasProcessed(true);

      // Create new tenant since TenantInitGate already confirmed it doesn't exist
      const tenantData = await createNewTenant();

      setTenantData(tenantData);
      onComplete(tenantData);
    } catch (error) {
      let errorMessage = "Unable to set up your tenant at this time";

      if (error instanceof Error) {
        // Use the specific error message from the API service
        errorMessage = error.message;
      }

      setError(errorMessage);
      onError?.(errorMessage);
      setHasProcessed(false); // Allow retry
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!hasProcessed) {
      if (existingTenantData) {
        // Tenant already exists, just complete the step
        console.log('Step1: Using existing tenant data:', existingTenantData.id);
        setTenantData(existingTenantData);
        setLoading(false);
        setHasProcessed(true);
        onComplete(existingTenantData);
      } else {
        // Tenant doesn't exist, create new one
        console.log('Step1: Creating new tenant for:', tenantId);
        processNewTenant();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tenantId, existingTenantData]); // Depend on both tenantId and existingTenantData

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-6 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
        <h2 className="text-xl font-bold text-gray-900 mb-2">
          Setting Up Tenant
        </h2>
        <p className="text-gray-600 text-sm">
          Setting up your tenant. Please wait...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-6 text-center text-red-600">
        <XCircle className="h-12 w-12 mb-4" />
        <h2 className="text-xl font-bold mb-2">Error</h2>
        <p className="text-sm mb-4">{error}</p>
        <button
          type="button"
          onClick={processNewTenant}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  if (tenantData) {
    const statusText = tenantData.isActive ? "Active" : "Inactive";

    // Define complete class names to ensure they're included in Tailwind build
    const statusClasses = tenantData.isActive
      ? {
          bgClass: "bg-green-100",
          textClass: "text-green-600",
          badgeBgClass: "bg-green-100",
          badgeTextClass: "text-green-800"
        }
      : {
          bgClass: "bg-yellow-100",
          textClass: "text-yellow-600",
          badgeBgClass: "bg-yellow-100",
          badgeTextClass: "text-yellow-800"
        };

    return (
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-start">
            <div
              className={`flex-shrink-0 ${statusClasses.bgClass} p-3 rounded-full`}
            >
              {tenantData.isActive ? (
                <CheckCircle className={`h-6 w-6 ${statusClasses.textClass}`} />
              ) : (
                <AlertCircle className={`h-6 w-6 ${statusClasses.textClass}`} />
              )}
            </div>
            <div className="ml-5 w-0 flex-1">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-gray-900">
                  Tenant Verified
                </h2>
                <span
                  className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${statusClasses.badgeBgClass} ${statusClasses.badgeTextClass}`}
                >
                  {statusText}
                </span>
              </div>
              <dl className="mt-3 grid grid-cols-1 gap-3 sm:grid-cols-2 text-sm">
                <div>
                  <dt className="font-medium text-gray-500 text-xs">
                    Tenant ID
                  </dt>
                  <dd className="mt-1 text-gray-900">{tenantData.id}</dd>
                </div>
                <div>
                  <dt className="font-medium text-gray-500 text-xs">
                    Admin Email
                  </dt>
                  <dd className="mt-1 text-gray-900">
                    {tenantData.adminEmail}
                  </dd>
                </div>
              </dl>

              {tenantData.product && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <h3 className="font-medium text-gray-900 mb-2">
                    Tenant Status
                  </h3>
                  <ul className="text-sm space-y-1">
                    <li className="flex items-center">
                      <span className="mr-2">•</span>
                      <span>
                        Users Imported:{" "}
                        {String(tenantData.product.isUserImported)}
                      </span>
                    </li>
                    <li className="flex items-center">
                      <span className="mr-2">•</span>
                      <span>
                        Roles Assigned:{" "}
                        {String(tenantData.product.isRoleAssigned)}
                      </span>
                    </li>
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default Step1TenantCheck;
