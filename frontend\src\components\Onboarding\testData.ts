// Test data for onboarding component when running standalone (not in iframe)
// This data is used for development and testing purposes
export const TENANT_ID = "black"
export const TEST_DATA = {
  // Basic authentication and tenant info
  tenantId: TENANT_ID,
  isAdmin: true,

  // JWT token for authentication
  token: 'eyJraWQiOiJZZHJLWGs0TmtXcHU1SmtYNkYzY0NiQStHXC9DUWJVaVpkRzVpR0JoSVkrST0iLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1Lw_vBz5wrlUtR4KXSXRDgxZaiPqi1_Lr5Vp6HxCQ56tFE2EfS6lN9ArEu7Q8i5xosijDHNy1ARVeMSvr4_oTAfX5u_QCbdbHQAaxJqL9WdtOTN9SNMf0_y8h-ek92emeMSWoNVD-WeOtb2QX2avRZ0M-ShdSIxvjX0Lq_9Z948jkui8YHaKKPSNBywPykV9K5vRRleM-AmVcv-eBH6BQONtw6ejBaNBk2zbvlhwbp5tbSZ1nk-qv5YGTFzQkelKFjLkiQURBfRPEFLmKQP6SMuuj5H4yQ7ziCq0EhhhmKLAetQvuF01fbeO3YyxYyhMM2EnOq_zRzrr7ZF5-bMHUQ',

  // Application details for testing
  // This represents the data that should be passed via postMessage
  applicationDetails: {
    id: "99eaa007-8b26-41a0-aaa9-9655d0597606",
    value: "MjlkNjU4ZGMtY2RjNy00ODU5LWI5NTAtZjVmNjllNDQ1YWMy",
    isActive: true,
    expiresAt: "2026-08-10T15:36:01.093Z",
    createdAt: "2025-08-10T15:36:01.093Z",
    isDeleted: false,
    tenantId: TENANT_ID,
    // This productId is used for the new /api/products/{id} API call
    productId: "68c787a3-9bd2-4c36-82ec-9e7ab372f9ac",
    product: {
      id: "68c787a3-9bd2-4c36-82ec-9e7ab372f9ac",
      name: "Agent",
      description: "Agent Performance",
      applicationUrl: "https://this-v2-dudqe6f6gscpd6fb.centralindia-01.azurewebsites.net",
      icon: "ic-chart-pie",
    },
    tenant: {
      id: TENANT_ID,
      name: TENANT_ID,
      adminEmail: "<EMAIL>",
      isActive: true,
      validUpto: "2026-05-19T09:45:32.046Z",
      issuer: null,
      identifier: TENANT_ID,
      displayPrefix: "LB",
    },
  }
} as const;

// Helper function to get test data values
export const getTestData = () => TEST_DATA;

// Helper function to check if we should use test data
export const shouldUseTestData = () => {
  return window.self !== window.top ? false : true; // Not in iframe = use test data
};
