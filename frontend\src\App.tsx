import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/layout/Header';
import TenantInitGate from './components/Onboarding/TenantInitGate';
import { OnboardingProvider } from './contexts/OnboardingContext';

function App() {
  return (
    <OnboardingProvider>
      <Router>
        <Routes>
          {/* Landing page is the onboarding flow - no header */}
          <Route path="" element={<TenantInitGate />} />
          <Route path="/onboarding" element={<TenantInitGate />} />
          <Route path="/onboarding/:tenantId" element={<TenantInitGate />} />

          {/* Dashboard route with header */}
          <Route path="/home" element={
            <div className="min-h-screen bg-gray-50">
              <div className="sticky top-0 z-50">
                <Header />
              </div>
              <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              </main>
            </div>
          } />
        </Routes>
      </Router>
    </OnboardingProvider>
  );
}

export default App;