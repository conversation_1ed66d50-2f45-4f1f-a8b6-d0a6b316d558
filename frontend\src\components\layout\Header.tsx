import React from "react";
import { NavLink } from "react-router-dom";
import { Package, Cuboid as Cube, Database, Home } from "lucide-react";

const Header: React.FC = () => {
  const navItems = [{ name: "Home", path: "/home", icon: Home }];

  return (
    <header className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-center h-16">
          <nav className="flex">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <NavLink
                  key={item.name}
                  to={item.path}
                  className={({ isActive }) =>
                    `inline-flex items-center px-4 py-2 text-sm font-medium border-b-2 ${isActive
                      ? "border-primary-500 text-primary-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    }`
                  }
                >
                  <Icon className="mr-1.5 h-4 w-4" />
                  {item.name}
                </NavLink>
              );
            })}
          </nav>

          <div></div>
        </div>
      </div>
    </header>
  );
};

export default Header;
