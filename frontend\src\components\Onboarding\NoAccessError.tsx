import React from "react";
import { Shield } from "lucide-react";

const NoAccessError: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <Shield className="w-12 h-12 text-orange-500 mx-auto mb-4" />
          <h1 className="text-xl font-bold text-gray-900 mb-4">
            Access Denied
          </h1>
          <p className="text-gray-600 mb-4 text-sm">
            You do not have permission to access.
          </p>
          <p className="text-xs text-gray-500">
            Please contact your administrator to request access.
          </p>
        </div>
      </div>
    </div>
  );
};

export default NoAccessError;
