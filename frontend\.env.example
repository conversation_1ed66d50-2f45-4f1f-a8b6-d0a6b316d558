# Environment Variables Template
# Copy this file to .env and fill in your actual values
# This file serves as documentation for all available environment variables

# API Configuration
VITE_API_BASE_URL=https://your-api-base-url.com/

# Application Configuration
VITE_APP_NAME=Product Builder App
VITE_APP_VERSION=0.1.0
VITE_APP_ENVIRONMENT=development

# Development settings
VITE_ENABLE_DEBUG=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_HOT_RELOAD=true
VITE_LOG_LEVEL=debug

# API endpoints
VITE_TENANT_API_ENDPOINT=/api/tenants/verify
VITE_USERS_API_ENDPOINT=/api/users/forproducts
VITE_ROLES_API_ENDPOINT=/api/roles

# Feature flags
VITE_ENABLE_MOCK_DATA=false
VITE_ENABLE_ERROR_BOUNDARY=true
VITE_ENABLE_PERFORMANCE_MONITORING=false

# Security settings
VITE_ENABLE_STRICT_MODE=true
VITE_ENABLE_CORS_VALIDATION=true
VITE_ENABLE_CSP=false

# Optional: Monitoring and analytics
VITE_SENTRY_DSN=your-sentry-dsn-here
VITE_GOOGLE_ANALYTICS_ID=your-ga-id-here

# Optional: Performance optimization
VITE_ENABLE_COMPRESSION=false
VITE_ENABLE_CACHING=false
VITE_CDN_URL=

# Optional: Rate limiting
VITE_API_RATE_LIMIT=1000
VITE_API_TIMEOUT=30000
