import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  CheckCircle,
  Building2,
  Users,
  Shield,
  XCircle,
} from "lucide-react";
import Step1TenantCheck from "./Step1TenantCheck";
import Step2UserImport from "./Step2UserImport";
import Step3RoleAssignment from "./Step3RoleAssignment";
import { TEST_DATA, shouldUseTestData } from "./testData";

interface OnboardingData {
  tenant: {
    id: string;
    name: string;
    adminEmail: string;
    isActive: boolean;
    validUpto: string;
    issuer: string | null;
  } | null;
  tenantId: string;
  users: Array<{
    id: string;
    userName: string;
    firstName: string;
    lastName: string;
    email: string;
    isActive: boolean;
    emailConfirmed: boolean;
    phoneNumber: string;
    phoneNumberConfirmed: boolean;
    imageUrl: string | null;
    isDeleted: boolean;
    lastModifiedOn: string;
    createdOn: string;
    createdBy: string;
    lastModifiedBy: string;
    isMFAEnabled: boolean;
    otp: string | null;
    otpUpdatedOn: string | null;
    timeZoneInfo: string | null;
    licenseNo: string | null;
    roles: string[];
  }>;
  tenantData?: {
    id: string;
    name: string;
    adminEmail: string;
    isActive: boolean;
    validUpto: string;
    issuer: string | null;
    product?: {
      id: string;
      name: string;
      isUserImported?: boolean;
      isRoleAssigned?: boolean;
      isOnboardCompleted?: boolean;
    };
  };
  roleAssignments: Record<string, string[]>;
}

interface OnboardingProps {
  initialTenantData: any;
  initialToken: string | null;
  initialTenantId: string | null;
  initialApplicationDetails: any | null;
}

const Onboarding: React.FC<OnboardingProps> = ({
  initialTenantData,
  initialToken,
  initialTenantId,
  initialApplicationDetails,
}) => {
  // Determine initial step based on tenant data and flags
  const getInitialStep = () => {
    if (!initialTenantData) return 1; // New tenant creation

    // If no product information available, stay at step 1
    if (!initialTenantData.product) return 1;

    // Check flags to determine which step to start at
    if (initialTenantData.product.isRoleAssigned || initialTenantData.product.isOnboardCompleted) {
      // If roles are already assigned or onboarding is completed, redirect will happen in useEffect
      return 3;
    } else if (initialTenantData.product.isUserImported) {
      // Users are imported, go to role assignment
      return 3;
    } else {
      // Users not imported yet, go to user import
      return 2;
    }
  };

  const [currentStep, setCurrentStep] = useState(getInitialStep());
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    tenant: initialTenantData,
    tenantId: initialTenantId || "",
    tenantData: initialTenantData,
    users: [],
    roleAssignments: {},
  });

  const token = initialToken;
  const tenantId = initialTenantId;
  const useTestData = shouldUseTestData();
  const [applicationDetails] = useState<any | null>(
    useTestData ? TEST_DATA.applicationDetails : initialApplicationDetails
  );
  const navigate = useNavigate();

  // Handle redirect logic when component mounts with existing tenant data
  React.useEffect(() => {
    if (initialTenantData?.product?.isOnboardCompleted) {
      navigate("/home");
      return;
    }

    if (initialTenantData?.product?.isRoleAssigned) {
      navigate("/home");
      return;
    }
  }, [initialTenantData, navigate]);

  const steps = [
    {
      number: 1,
      title: "Step 1",
      description: "Check Tenant",
      icon: Building2,
    },
    { number: 2, title: "Step 2", description: "Import Users", icon: Users },
    { number: 3, title: "Step 3", description: "Assign Roles", icon: Shield },
  ];

  const handleStepComplete = (step: number, data: any) => {
    if (step === 1) {
      setOnboardingData((prev) => ({
        ...prev,
        tenant: data.tenant || data,
        tenantData: data,
        tenantId: data.id || data.tenant?.id || "",
      }));

      // If no product information available, stay at step 1
      if (!data.product) {
        console.error("Product information is required to proceed with onboarding. Please contact your administrator.");
        return;
      }

      // Check if onboarding is already completed
      if (data.product.isOnboardCompleted) {
        navigate("/home");
        return;
      }

      // Check if we should skip to home if roles are already assigned
      if (data.product.isRoleAssigned) {
        navigate("/home");
      } else if (data.product.isUserImported) {
        // Users are imported, go to role assignment
        setCurrentStep(3);
      } else {
        // Users not imported yet, go to user import
        setCurrentStep(2);
      }
    } else if (step === 2) {
      setOnboardingData((prev) => ({
        ...prev,
        users: data as OnboardingData["users"],
      }));

      // Check if we should skip to home if onboarding is completed or roles are already assigned
      if (
        onboardingData.tenantData?.product?.isOnboardCompleted ||
        onboardingData.tenantData?.product?.isRoleAssigned
      ) {
        navigate("/home");
      } else {
        // Proceed to step 3 if onboarding is not completed and roles are not assigned
        setCurrentStep(3);
      }
    } else if (step === 3) {
      // Data from Step3RoleAssignment is the role assignments
      setOnboardingData((prev) => ({
        ...prev,
        roleAssignments: data as OnboardingData["roleAssignments"],
      }));
      // Navigate to home on completion
      navigate("/home");
    }
  };



  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-12">
      {steps.map((step, index) => {
        const IconComponent = step.icon;
        const isCompleted = step.number < currentStep;
        const isCurrent = step.number === currentStep;

        return (
          <div key={step.number} className="flex items-center">
            <div className="flex flex-col items-center">
              {/* Step Circle */}
              <div
                className={`relative flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-500 ${isCompleted
                    ? "step-completed border-primary-600 text-white"
                    : isCurrent
                      ? "step-current border-primary-500 text-white"
                      : "bg-white border-gray-300 text-gray-400 hover:border-gray-400 hover:bg-gray-50"
                  }`}
              >
                {isCompleted ? (
                  <CheckCircle className="w-6 h-6" />
                ) : (
                  <IconComponent className="w-6 h-6" />
                )}
              </div>

              {/* Step Label */}
              <div className="mt-4 text-center">
                <div className="text-xxs font-medium text-gray-500">
                  {step.title}
                </div>
                <div className="text-xs font-medium">{step.description}</div>
                {isCompleted && (
                  <div className="text-xxs text-primary-700 mt-1 font-medium">
                    ✓ Completed
                  </div>
                )}
                {isCurrent && (
                  <div className="text-xxs text-yellow-500 mt-1 font-medium">
                    In Progress
                  </div>
                )}
                {!isCompleted && !isCurrent && (
                  <div className="text-xxs mt-1 font-medium">&nbsp;</div>
                )}
              </div>
            </div>

            {/* Connecting Line */}
            {index < steps.length - 1 && (
              <div className="flex items-center mx-8">
                <div className="flex space-x-1">
                  {Array.from({ length: 8 }).map((_, dotIndex) => (
                    <div
                      key={`step-${index}-dot-${dotIndex}`}
                      className={`w-1.5 h-1.5 rounded-full transition-all duration-500 ${isCompleted ? "bg-primary-600 shadow-sm" : "bg-gray-300"
                        }`}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  const renderCurrentStep = () => {
    // Skip to home if roles are already assigned
    if (onboardingData.tenantData?.product?.isRoleAssigned) {
      navigate("/home");
      return null;
    }

    // If we're past step 1 but have no product information, show error
    if (currentStep > 1 && !onboardingData.tenantData?.product) {
      return (
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <XCircle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Product Information Required</h3>
            <p className="text-sm text-gray-600 mb-4">
              Product information is required to proceed with onboarding. Please contact your administrator.
            </p>
            <button
              type="button"
              onClick={() => setCurrentStep(1)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Back to Step 1
            </button>
          </div>
        </div>
      );
    }

    switch (currentStep) {
      case 1:
        return (
          <Step1TenantCheck
            tenantId={tenantId!}
            existingTenantData={initialTenantData}
            applicationDetails={applicationDetails}
            onComplete={(data) => handleStepComplete(1, data)}
          />
        );
      case 2:
        return (
          <Step2UserImport
            tenantId={tenantId!}
            tenantData={onboardingData.tenantData}
            applicationDetails={applicationDetails}
            onComplete={(data) => handleStepComplete(2, data)}
          />
        );
      case 3:
        return (
          <Step3RoleAssignment
            users={onboardingData.users}
            tenantId={tenantId!}
            token={token || ""}
            applicationDetails={applicationDetails}
            tenantData={onboardingData.tenantData}
            onComplete={(data) => handleStepComplete(3, data)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-xl font-bold text-gray-900">
            {applicationDetails?.product?.name} Onboarding
          </h1>
          <p className="text-base text-gray-600">
            {tenantId
              ? `Setting up tenant: ${tenantId}`
              : "Let's get your tenant set up in just a few simple steps"}
          </p>
        </div>

        {renderStepIndicator()}

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
