import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Onboarding from "./Onboarding";
import NoAccessError from "./NoAccessError";
import { useOnboarding } from "../../contexts/OnboardingContext";
import { apiService } from "../../services/apiService";

import { TEST_DATA, shouldUseTestData } from "./testData";

interface TenantData {
  id: string;
  name: string;
  adminEmail: string;
  isActive: boolean;
  validUpto: string;
  issuer: string | null;
  product?: {
    id: string;
    name: string;
    isUserImported?: boolean;
    isRoleAssigned?: boolean;
    isOnboardCompleted?: boolean;
  };
}

interface TenantCheckState {
  loading: boolean;
  error: string | null;
  tenantData: TenantData | null;
  isAdmin: boolean | null;
  token: string | null;
  tenantId: string | null;
  applicationDetails: any | null;
  validationComplete: boolean;
}

const TenantInitGate: React.FC = () => {
  const navigate = useNavigate();
  const { tenantId: routeTenantId } = useParams<{ tenantId?: string }>();
  const useTestData = shouldUseTestData();
  const { fetchTenant } = useOnboarding();

  const [state, setState] = useState<TenantCheckState>({
    loading: true, // Always start with loading true
    error: null,
    tenantData: null,
    isAdmin: useTestData ? TEST_DATA.isAdmin : null,
    token: useTestData ? TEST_DATA.token : null,
    tenantId: useTestData ? TEST_DATA.tenantId : routeTenantId || null,
    applicationDetails: useTestData ? TEST_DATA.applicationDetails : null,
    validationComplete: false,
  });

  // Listen for postMessage data when in iframe
  useEffect(() => {
    if (useTestData) return; // Skip postMessage when using test data

    const handleMessage = (event: MessageEvent) => {
      const {
        tenantId: messageTenantId,
        isAdmin: messageIsAdmin,
        token: messageToken,
        applicationDetails: messageApplicationDetails,
      } = event.data;

      setState((prev) => ({
        ...prev,
        tenantId: messageTenantId || prev.tenantId,
        isAdmin:
          typeof messageIsAdmin === "boolean"
            ? messageIsAdmin
            : typeof messageIsAdmin === "string"
              ? messageIsAdmin.toLowerCase() === "true"
              : prev.isAdmin,
        token: messageToken || prev.token,
        applicationDetails: messageApplicationDetails || prev.applicationDetails,
      }));
    };

    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, [useTestData]);

  // Perform tenant check when we have tenantId
  useEffect(() => {
    if (!state.tenantId || state.validationComplete) return;

    const checkTenant = async () => {
      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));

        // Use the context to fetch tenant data with caching
        const tenantData = await fetchTenant(state.tenantId!, true);

        if (tenantData) {
          // Get product status flags from the new API endpoint
          if (state.applicationDetails?.productId) {
            try {
              const productData = await apiService.getProductById(
                state.applicationDetails.productId,
                state.tenantId!,
                true // Use cache to prevent duplicate calls
              );

              // Ensure tenant data has a product object and update with status flags from the new API
              if (productData) {
                if (!tenantData.product) {
                  tenantData.product = {
                    id: state.applicationDetails.productId,
                    name: productData.name || '',
                    isUserImported: false,
                    isRoleAssigned: false,
                    isOnboardCompleted: false,
                  };
                }
                tenantData.product.isUserImported = productData.isUserImported || false;
                tenantData.product.isRoleAssigned = productData.isRoleAssigned || false;
                tenantData.product.isOnboardCompleted = productData.isOnboardCompleted || false;
              }
            } catch (productError) {
              console.warn('Failed to fetch product status flags:', productError);

              // Check if this is a "Product not found" error
              const errorMessage = productError instanceof Error ? productError.message : '';
              if (errorMessage.includes('Product not found')) {
                console.error('Product not found - onboarding will stop at step 1');
                // Don't create any product object - this will cause onboarding to stop at step 1
                tenantData.product = undefined;
              } else {
                // For other errors (network issues, server errors), create minimal product object
                if (state.applicationDetails?.productId) {
                  if (!tenantData.product) {
                    tenantData.product = {
                      id: state.applicationDetails.productId,
                      name: '',
                      isUserImported: false,
                      isRoleAssigned: false,
                      isOnboardCompleted: false,
                    };
                  }
                }
              }
            }
          }

          // Check if onboarding is already completed
          if (tenantData.product?.isOnboardCompleted) {
            navigate("/home");
            return;
          }

          // Check admin access for existing tenant
          if (state.isAdmin === false) {
            setState((prev) => ({
              ...prev,
              loading: false,
              error: "Access denied. Admin privileges required.",
            }));
            return;
          }

          // All checks passed, set tenant data
          setState((prev) => ({
            ...prev,
            loading: false,
            tenantData,
            error: null,
            validationComplete: true,
          }));
        } else {
          // Tenant not found - handle based on admin status
          if (state.isAdmin === true) {
            // Admin user: proceed to onboarding (will create new tenant)
            setState((prev) => ({
              ...prev,
              loading: false,
              tenantData: null, // No existing tenant data
              error: null,
              validationComplete: true,
            }));
          } else {
            // Non-admin user: show no access
            setState((prev) => ({
              ...prev,
              loading: false,
              error: "Access denied. Admin privileges required.",
              validationComplete: true,
            }));
          }
        }
      } catch (error) {
        // Handle API errors based on admin status
        if (state.isAdmin === true) {
          // Admin user: proceed to onboarding (will create new tenant)
          setState((prev) => ({
            ...prev,
            loading: false,
            tenantData: null, // No existing tenant data
            error: null,
            validationComplete: true,
          }));
        } else {
          // Non-admin user: show no access
          setState((prev) => ({
            ...prev,
            loading: false,
            error: "Access denied. Admin privileges required.",
            validationComplete: true,
          }));
        }
      }
    };

    checkTenant();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.tenantId, state.isAdmin]); // Removed navigate and fetchTenant to prevent re-renders

  // Show loading screen while checking tenant
  if (state.loading || !state.validationComplete) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Validating Tenant
          </h3>
          <p className="text-gray-600">
            Checking tenant permissions and onboarding status...
          </p>
        </div>
      </div>
    );
  }

  // Show error states (No Access)
  if (state.error) {
    return <NoAccessError />;
  }

  // Show no access if admin check failed
  if (state.isAdmin === false) {
    return <NoAccessError />;
  }

  // All checks passed, render onboarding
  // tenantData can be null for new tenant creation (admin users when API fails)
  return (
    <Onboarding
      initialTenantData={state.tenantData}
      initialToken={state.token}
      initialIsAdmin={state.isAdmin}
      initialTenantId={state.tenantId}
      initialApplicationDetails={state.applicationDetails}
    />
  );
};

export default TenantInitGate;
