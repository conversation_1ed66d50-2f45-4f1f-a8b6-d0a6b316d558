import React from 'react';
import { ToggleProps } from '../../types';

const Toggle: React.FC<ToggleProps> = ({ checked, onChange, label }) => {
  return (
    <div className="flex items-center">
      <button
        type="button"
        className={`toggle ${checked ? 'toggle-active' : ''}`}
        onClick={() => onChange(!checked)}
        role="switch"
        aria-checked={checked}
      >
        <span className={`toggle-bg ${checked ? 'toggle-active' : ''}`} />
        <span
          className={`toggle-circle ${checked ? 'translate-x-5' : 'translate-x-0'}`}
        />
      </button>
      {label && <span className="ml-2 text-sm">{label}</span>}
    </div>
  );
};

export default Toggle;