import React, { useState } from 'react';
import { FilterProps } from '../../types';
import { Filter } from 'lucide-react';

const Filters: React.FC<FilterProps> = ({ onFilter, filterOptions }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState<Record<string, any>>({});

  const handleFilterChange = (field: string, value: any) => {
    setFilters((prev) => ({ ...prev, [field]: value }));
  };

  const handleApplyFilters = () => {
    onFilter(filters);
    setIsOpen(false);
  };

  const handleClearFilters = () => {
    setFilters({});
    onFilter({});
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        type="button"
        className="btn btn-secondary"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Filter size={18} />
        <span>Filters</span>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-72 bg-white rounded-md shadow-lg z-10 border border-gray-200">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Filters</h3>
              <button
                type="button"
                className="text-gray-500 hover:text-gray-700"
                onClick={() => setIsOpen(false)}
              >
                &times;
              </button>
            </div>

            <div className="space-y-4">
              {filterOptions.map((option) => (
                <div key={option.field} className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700">
                    {option.label}
                  </label>
                  {option.type === 'text' && (
                    <input
                      type="text"
                      className="input"
                      value={filters[option.field] || ''}
                      onChange={(e) => handleFilterChange(option.field, e.target.value)}
                    />
                  )}
                  {option.type === 'select' && (
                    <select
                      className="input"
                      value={filters[option.field] || ''}
                      onChange={(e) => handleFilterChange(option.field, e.target.value)}
                    >
                      <option value="">All</option>
                      {option.options?.map((opt) => (
                        <option key={opt.value} value={opt.value}>
                          {opt.label}
                        </option>
                      ))}
                    </select>
                  )}
                  {option.type === 'boolean' && (
                    <select
                      className="input"
                      value={filters[option.field] || ''}
                      onChange={(e) => handleFilterChange(option.field, e.target.value === 'true')}
                    >
                      <option value="">All</option>
                      <option value="true">Yes</option>
                      <option value="false">No</option>
                    </select>
                  )}
                </div>
              ))}
            </div>

            <div className="flex justify-between mt-6">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={handleClearFilters}
              >
                Clear
              </button>
              <button
                type="button"
                className="btn btn-primary"
                onClick={handleApplyFilters}
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Filters;