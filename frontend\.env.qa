# Staging Environment Variables
# This file is loaded when NODE_ENV=staging or when deploying to staging environment

# API Configuration (Staging)
VITE_API_BASE_URL=https://lrb-identity-api-staging.azurewebsites.net/

# Application Configuration
VITE_APP_NAME=Product Builder App (Staging)
VITE_APP_VERSION=0.1.0
VITE_APP_ENVIRONMENT=staging

# Staging-specific settings
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_HOT_RELOAD=false
VITE_LOG_LEVEL=warn

# Staging API endpoints
VITE_TENANT_API_ENDPOINT=/api/tenants/verify
VITE_USERS_API_ENDPOINT=/api/users/forproducts
VITE_ROLES_API_ENDPOINT=/api/roles

# Staging feature flags
VITE_ENABLE_MOCK_DATA=false
VITE_ENABLE_ERROR_BOUNDARY=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Security settings for staging
VITE_ENABLE_STRICT_MODE=true
VITE_ENABLE_CORS_VALIDATION=true

# Monitoring and logging
VITE_SENTRY_DSN=your-staging-sentry-dsn-here
VITE_GOOGLE_ANALYTICS_ID=your-staging-ga-id-here
